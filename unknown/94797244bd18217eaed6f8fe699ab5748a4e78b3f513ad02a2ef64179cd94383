import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ComingSoon from '@/components/ui/ComingSoon';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Subscriptions | KaziSync',
  description: 'Manage company subscriptions and billing',
};

export default function SubscriptionsPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <ComingSoon
          title="Subscriptions Management"
          description="Manage company subscriptions, billing plans, and payment processing."
          icon="💰"
          backLink="/dashboard/super-admin"
          backLinkText="Back to Super Admin Dashboard"
        />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
