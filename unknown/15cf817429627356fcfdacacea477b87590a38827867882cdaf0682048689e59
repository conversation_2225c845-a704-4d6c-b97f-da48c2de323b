'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import { getCountries, getPayrollPolicyTypes } from '@/lib/payroll';
import { useAuth } from '@/contexts/AuthContext';

const SuperAdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalCountries: 0,
    readyCountries: 0,
    totalPolicyTypes: 0,
    activePolicyTypes: 0,
    isLoading: true
  });
  const [error, setError] = useState('');

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setError('');
      const [countriesResponse, policyTypesResponse] = await Promise.all([
        getCountries(),
        getPayrollPolicyTypes()
      ]);

      const readyCountries = countriesResponse.countries.filter(c => c.payroll_status?.is_ready).length;
      const activePolicyTypes = policyTypesResponse.policyTypes.filter(pt => pt.is_active).length;

      setStats({
        totalCountries: countriesResponse.totalCount,
        readyCountries,
        totalPolicyTypes: policyTypesResponse.totalCount,
        activePolicyTypes,
        isLoading: false
      });
    } catch (error: any) {
      console.error('Error fetching dashboard data:', error);
      setError(error.message || 'Failed to load dashboard data. Please try again.');
      setStats(prev => ({ ...prev, isLoading: false }));
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const dashboardStats = [
    {
      title: 'Total Countries',
      value: stats.totalCountries.toString(),
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Ready Countries',
      value: stats.readyCountries.toString(),
      change: '',
      changeType: 'positive' as const
    },
    {
      title: 'Policy Types',
      value: stats.totalPolicyTypes.toString(),
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Active Policies',
      value: stats.activePolicyTypes.toString(),
      change: '',
      changeType: 'positive' as const
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-600">Manage global system settings and configurations</p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
              <button
                onClick={fetchDashboardData}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={stats.isLoading}
          />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Country Management */}
        <DashboardCard title="Country Management">
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Manage countries, their payroll settings, and regional configurations.
            </p>

            <div className="grid grid-cols-1 gap-3">
              <Link
                href="/dashboard/super-admin/countries"
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-900">Countries</h3>
                    <p className="text-sm text-gray-500">Add and manage countries</p>
                  </div>
                </div>
              </Link>

              <Link
                href="/dashboard/super-admin/countries/payroll-policies"
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-900">Payroll Policies</h3>
                    <p className="text-sm text-gray-500">Global payroll policy types</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </DashboardCard>

        {/* Companies Management */}
        <DashboardCard title="Companies Management">
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Manage companies and their devices across the platform.
            </p>

            <div className="grid grid-cols-1 gap-3">
              <Link
                href="/dashboard/super-admin/companies"
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-900">Companies</h3>
                    <p className="text-sm text-gray-500">View and manage all companies</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </DashboardCard>
      </div>

      {/* Additional Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Settings */}
        <DashboardCard title="System Settings">
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Configure global system settings and preferences.
            </p>

            <div className="grid grid-cols-1 gap-3">
              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-500">Global Settings</h3>
                    <p className="text-sm text-gray-400">Coming soon</p>
                  </div>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-500">User Management</h3>
                    <p className="text-sm text-gray-400">Coming soon</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DashboardCard>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Overview */}
        <DashboardCard title="System Overview">
          <div className="space-y-4">
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-gray-600">Countries Configured</span>
              <span className="text-sm font-medium text-gray-900">
                {stats.readyCountries} / {stats.totalCountries}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{
                  width: stats.totalCountries > 0
                    ? `${(stats.readyCountries / stats.totalCountries) * 100}%`
                    : '0%'
                }}
              ></div>
            </div>

            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-gray-600">Active Policy Types</span>
              <span className="text-sm font-medium text-gray-900">
                {stats.activePolicyTypes} / {stats.totalPolicyTypes}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: stats.totalPolicyTypes > 0
                    ? `${(stats.activePolicyTypes / stats.totalPolicyTypes) * 100}%`
                    : '0%'
                }}
              ></div>
            </div>
          </div>
        </DashboardCard>

        {/* Quick Actions */}
        <DashboardCard title="Quick Actions">
          <div className="grid grid-cols-2 gap-4">
            <Link
              href="/dashboard/super-admin/countries"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <svg className="mx-auto h-8 w-8 text-blue-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Add Country</p>
              <p className="text-xs text-gray-500">Configure new country</p>
            </Link>

            <Link
              href="/dashboard/super-admin/countries/payroll-policies"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <svg className="mx-auto h-8 w-8 text-purple-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Policy Type</p>
              <p className="text-xs text-gray-500">Create policy type</p>
            </Link>
          </div>
        </DashboardCard>
      </div>

      {/* Information Card */}
      <DashboardCard title="Super Admin Guide">
        <div className="text-sm text-gray-600 space-y-2">
          <p>
            <strong>🌍 Country Management:</strong> Add and configure countries with their specific payroll settings, time zones, and currencies.
          </p>
          <p>
            <strong>📋 Payroll Policies:</strong> Create global payroll policy types that can be used across different countries and companies.
          </p>
          <p>
            <strong>⚙️ System Configuration:</strong> Manage global system settings that affect all users and companies in the platform.
          </p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default SuperAdminDashboard;
