'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import EmployeeRegistrationModal from '@/components/employee/EmployeeRegistrationModal';
import EmployeeDetailsModal from '@/components/employee/EmployeeDetailsModal';
import EmployeeEditModal from '@/components/employee/EmployeeEditModal';
import Link from 'next/link';

// Define department interface
interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
  id_number?: string | null;
}

interface EmployeeResponse {
  code: number;
  extend: {
    employees: Employee[];
    pagination: {
      has_next: boolean;
      has_prev: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  msg: string;
}

const EmployeesContent: React.FC = () => {
  const { companies, user } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [error, setError] = useState('');
  const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [employeeToDeactivate, setEmployeeToDeactivate] = useState<Employee | null>(null);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const openEmployeeModal = () => setIsEmployeeModalOpen(true);
  const closeEmployeeModal = () => setIsEmployeeModalOpen(false);

  // Open deactivate confirmation
  const openDeactivateModal = (employee: Employee) => {
    setEmployeeToDeactivate(employee);
    setIsDeactivateModalOpen(true);
  };
  const closeDeactivateModal = () => {
    setEmployeeToDeactivate(null);
    setIsDeactivateModalOpen(false);
  };

  // Open view employee modal
  const openViewModal = (employeeId: string) => {
    setSelectedEmployeeId(employeeId);
    setIsViewModalOpen(true);
  };
  const closeViewModal = () => {
    setSelectedEmployeeId(null);
    setIsViewModalOpen(false);
  };

  // Open edit employee modal
  const openEditModal = (employeeId: string) => {
    setSelectedEmployeeId(employeeId);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedEmployeeId(null);
    setIsEditModalOpen(false);
  };

  // Mock data for now - in a real app, this would come from an API
  const mockEmployees: Employee[] = [
    {
      employee_id: '1',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      email: '<EMAIL>',
      phone_number: '************',
      position: 'Software Developer',
      status: 'active',
      hire_date: '2023-01-15',
      department_id: 'dev-001'
    },
    {
      employee_id: '2',
      first_name: 'Jane',
      last_name: 'Smith',
      full_name: 'Jane Smith',
      email: '<EMAIL>',
      phone_number: '************',
      position: 'Marketing Specialist',
      status: 'active',
      hire_date: '2023-02-20',
      department_id: 'mkt-001'
    },
    {
      employee_id: '3',
      first_name: 'Robert',
      last_name: 'Wilson',
      full_name: 'Robert Wilson',
      email: '<EMAIL>',
      phone_number: '************',
      position: 'Financial Analyst',
      status: 'active',
      hire_date: '2023-03-10',
      department_id: 'fin-001'
    },
    {
      employee_id: '4',
      first_name: 'Emily',
      last_name: 'Davis',
      full_name: 'Emily Davis',
      email: '<EMAIL>',
      phone_number: '************',
      position: 'HR Assistant',
      status: 'active',
      hire_date: '2023-04-05',
      department_id: 'hr-001'
    },
    {
      employee_id: '5',
      first_name: 'Michael',
      last_name: 'Brown',
      full_name: 'Michael Brown',
      email: '<EMAIL>',
      phone_number: '************',
      position: 'Project Manager',
      status: 'active',
      hire_date: '2023-05-15',
      department_id: 'pm-001'
    }
  ];

  useEffect(() => {
    // Fetch employees and departments when the component mounts
    fetchEmployees();
    fetchDepartments();
  }, [companies]);

  // Filter employees when selectedDepartment changes
  useEffect(() => {
    if (selectedDepartment === '') {
      setFilteredEmployees(employees);
    } else {
      setFilteredEmployees(employees.filter(emp => emp.department_id === selectedDepartment));
    }
  }, [selectedDepartment, employees]);

  // Function to fetch departments
  const fetchDepartments = async () => {
    try {
      setIsLoadingDepartments(true);

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        console.error('No company found');
        setIsLoadingDepartments(false);
        return;
      }

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        console.error('Authentication required');
        setIsLoadingDepartments(false);
        return;
      }

      const response = await apiGet<{departments: Department[], success: boolean}>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.departments) {
        setDepartments(response.departments);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Fetch employees when the component mounts or when pagination changes
  useEffect(() => {
    fetchEmployees();
    fetchDepartments();
  }, [companies, currentPage, itemsPerPage]);

  // Function to deactivate an employee
  const handleDeactivateEmployee = async () => {
    if (!employeeToDeactivate) return;

    try {
      setIsLoading(true);

      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const deactivateData = {
        company_id: companyId,
        status: 'inactive'
      };

      // Use PATCH method for deactivation
      const { apiPatch } = await import('@/lib/api');

      // Use PATCH method with the correct endpoint
      await apiPatch(`api/employees/${employeeToDeactivate.employee_id}`, deactivateData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Update the employee status in the local state
      setEmployees(employees.map(emp =>
        emp.employee_id === employeeToDeactivate.employee_id
          ? { ...emp, status: 'inactive' }
          : emp
      ));

      // Close the confirmation dialog
      closeDeactivateModal();
    } catch (error: any) {

      setError(error.message || 'Failed to deactivate employee');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch employees from API
  const fetchEmployees = async () => {
    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      // Add pagination parameters to the API request
      const response = await apiGet<EmployeeResponse>(
        `api/employees?company_id=${companyId}&page=${currentPage}&per_page=${itemsPerPage}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);

        // Set pagination information
        if (response.extend.pagination) {
          setTotalPages(response.extend.pagination.pages);
          // Update itemsPerPage if it's different from the API response
          if (response.extend.pagination.per_page !== itemsPerPage) {
            setItemsPerPage(response.extend.pagination.per_page);
          }
        }
      } else {
        // If API call succeeds but doesn't return the expected data, fall back to mock data

        setEmployees(mockEmployees);
        setTotalPages(1);
      }
    } catch (error: any) {
      console.error('Error fetching employees:', error);

      // Import the utility to check if error should be displayed
      const { shouldDisplayError } = await import('@/lib/auth-utils');

      // Only show error if it's not a session expiration or expected empty state
      if (shouldDisplayError(error)) {
        setError(error.message || 'Failed to fetch employees');
      }

      // Don't fall back to mock data for new companies - show empty state instead
      setEmployees([]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Employee Registration Modal */}
      <EmployeeRegistrationModal
        isOpen={isEmployeeModalOpen}
        onClose={closeEmployeeModal}
        onSuccess={() => {
          closeEmployeeModal();
          // Refresh employee list after adding a new employee
          fetchEmployees();
        }}
      />

      {/* Employee Details Modal */}
      <EmployeeDetailsModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        employeeId={selectedEmployeeId}
      />

      {/* Employee Edit Modal */}
      <EmployeeEditModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          // Refresh employee list after editing
          fetchEmployees();
        }}
        employeeId={selectedEmployeeId}
      />

      {/* Deactivate Employee Confirmation Modal */}
      {isDeactivateModalOpen && employeeToDeactivate && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={closeDeactivateModal}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            {/* Modal panel */}
            <div
              className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
              onClick={e => e.stopPropagation()}
            >
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Deactivate Employee</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to deactivate {employeeToDeactivate.full_name}? They will no longer be able to access the system.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeactivateEmployee}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deactivating...' : 'Deactivate'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeactivateModal}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Employee Management</h1>
        <div className="flex space-x-3">
          <Link
            href="/dashboard/hr/employees/accounts"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Accounts
          </Link>
          <Link
            href="/dashboard/hr/employees/users"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Manage Users
          </Link>
          <button
            className="btn-primary py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center"
            onClick={openEmployeeModal}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Employee
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Employees</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <DashboardCard title="Employee List">
        <div className="mb-4">
          <label htmlFor="department-filter" className="block text-sm font-medium text-secondary-dark mb-1">
            Filter by Department
          </label>
          <div className="flex space-x-2">
            <select
              id="department-filter"
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-64 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Departments</option>
              {isLoadingDepartments ? (
                <option disabled>Loading departments...</option>
              ) : departments.length === 0 ? (
                <option disabled>No departments available</option>
              ) : (
                departments.map(dept => (
                  <option key={dept.department_id} value={dept.department_id}>
                    {dept.name}
                  </option>
                ))
              )}
            </select>
            {selectedDepartment && (
              <button
                onClick={() => setSelectedDepartment('')}
                className="btn-outline py-1 px-3 text-sm"
              >
                Clear Filter
              </button>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading employees...</p>
          </div>
        ) : filteredEmployees.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No employees found.</p>
            <button
              onClick={openEmployeeModal}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Add your first employee
            </button>
          </div>
        ) : (
          <div className="relative flex flex-col h-[70vh] md:h-[60vh]">
            {/* Fixed Pagination at the bottom */}
            <div className="sticky bottom-0 left-0 right-0 bg-white px-6 py-4 flex items-center justify-between border-t border-gray-200 z-10">
              <div className="flex-1 flex justify-between items-center">
                <div>
                  <p className="text-sm text-secondary">
                    Showing page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{totalPages}</span>
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1 || isLoading}
                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                      currentPage === 1 || isLoading
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-secondary-dark hover:bg-gray-50'
                    }`}
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages || isLoading}
                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                      currentPage === totalPages || isLoading
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-secondary-dark hover:bg-gray-50'
                    }`}
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>

            {/* Scrollable Table Container */}
            <div className="overflow-y-auto overflow-x-auto flex-1">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">{employee.full_name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">{employee.position || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {departments.find(d => d.department_id === employee.department_id)?.name || 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary">{employee.email || 'N/A'}</div>
                        <div className="text-xs text-secondary">{employee.phone_number || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          employee.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {employee.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex flex-col md:flex-row md:justify-end space-y-2 md:space-y-0 md:space-x-3">
                          <button
                            className="text-primary hover:text-primary-dark"
                            onClick={() => openViewModal(employee.employee_id)}
                          >
                            View
                          </button>
                          <button
                            className="text-secondary-dark hover:text-secondary"
                            onClick={() => openEditModal(employee.employee_id)}
                          >
                            Edit
                          </button>
                          {employee.status === 'active' ? (
                            <button
                              className="text-red-600 hover:text-red-800"
                              onClick={() => openDeactivateModal(employee)}
                            >
                              Deactivate
                            </button>
                          ) : (
                            <button
                              className="text-gray-400 cursor-not-allowed"
                              disabled
                            >
                              Deactivated
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default EmployeesContent;
